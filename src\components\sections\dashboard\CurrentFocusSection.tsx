"use client";

import React, { useRef, useMemo, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Mail, MapPin, Globe, Sparkles, Code2, Database, Monitor, Zap, Heart } from 'lucide-react';
import { cn } from '@/lib/utils';
import { technicalSkills } from '@/data/skills/skillsData';
import createGlobe, { COBEOptions } from 'cobe';

// --- TYPE DEFINITIONS ---
type BaseSectionProps = {
    className?: string;
};

// --- FIXED ANIMATION VARIANTS ---
const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.1,
            delayChildren: 0.2
        }
    }
};

const cardVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.95 },
    visible: { 
        opacity: 1, 
        y: 0, 
        scale: 1,
        transition: { 
            duration: 0.6, 
            ease: [0.25, 0.46, 0.45, 0.94] as const
        }
    }
};

// --- ENHANCED SERVICES DATA ---
const services = [
    {
        title: "API Architecture",
        description: "Designing robust and scalable REST & GraphQL APIs with proper authentication and rate limiting.",
        icon: Code2,
        gradient: "from-blue-500/20 to-cyan-500/20"
    },
    {
        title: "Database Design",
        description: "Structuring relational and NoSQL databases for optimal performance and scalability.",
        icon: Database,
        gradient: "from-green-500/20 to-emerald-500/20"
    },
    {
        title: "System Monitoring",
        description: "Implementing comprehensive logging, metrics, and real-time analytics dashboards.",
        icon: Monitor,
        gradient: "from-purple-500/20 to-pink-500/20"
    },
    {
        title: "Performance Optimization",
        description: "Optimizing application performance through caching, CDN, and code splitting strategies.",
        icon: Zap,
        gradient: "from-orange-500/20 to-red-500/20"
    },
    {
        title: "User Experience",
        description: "Creating intuitive interfaces with accessibility and responsive design principles.",
        icon: Heart,
        gradient: "from-pink-500/20 to-rose-500/20"
    },
    {
        title: "Cloud Infrastructure",
        description: "Deploying and managing applications on AWS, Vercel, and other cloud platforms.",
        icon: Globe,
        gradient: "from-indigo-500/20 to-blue-500/20"
    },
    {
        title: "Authentication",
        description: "Implementing secure user authentication with JWT, OAuth, and multi-factor authentication.",
        icon: Code2,
        gradient: "from-yellow-500/20 to-orange-500/20"
    },
    {
        title: "Real-time Features",
        description: "Building live chat, notifications, and collaborative features using WebSockets.",
        icon: Zap,
        gradient: "from-cyan-500/20 to-blue-500/20"
    }
];

// --- UPDATED BENTO CARD COMPONENT ---
const BentoCard = ({
    className,
    children,
}: {
    className?: string;
    children: React.ReactNode;
}) => {
    return (
        <motion.div
            variants={cardVariants}
            className={cn(
                // This parent has `relative` and `overflow-hidden`, creating the boundary
                "group relative flex flex-col bg-neutral-900/50 backdrop-blur-lg shadow-[inset_0_0_20px_0_rgba(255,255,255,0.08)] rounded-2xl p-6 overflow-hidden transition-all duration-300",
                className
            )}
        >
            <div className="absolute inset-0 bg-gradient-to-br from-primary/4 via-transparent to-accent/4 rounded-2xl" />
            <div className="relative z-10 flex flex-col h-full">
                {children}
            </div>
        </motion.div>
    );
};


// --- BENTO GRID CARD COMPONENTS ---

const CollaborationCard = () => {
    return (
        <BentoCard className="md:col-span-2 md:row-span-1">
            <div className="flex items-center justify-between h-full">
                <div className="flex-1">
                    <div className="flex items-center gap-3 mb-4">
                        <div className="p-2.5 bg-gradient-to-br from-primary/20 to-accent/20 rounded-xl backdrop-blur-sm border border-primary/20">
                            <Heart className="w-5 h-5 text-accent" />
                        </div>
                        <div>
                            <h3 className="text-heading text-lg font-semibold text-foreground">Collaboration</h3>
                            <p className="text-sm text-muted-foreground">Building together</p>
                        </div>
                    </div>
                    <p className="text-muted-foreground text-sm leading-relaxed font-geometric max-w-xs">
                        I prioritize client collaboration, fostering open communication
                        throughout the development process.
                    </p>
                </div>
                <div className="relative flex items-center justify-center w-32 h-32">
                    <motion.div
                        className="absolute w-24 h-24 rounded-full border border-primary/30"
                        animate={{ rotate: 360 }}
                        transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                    />
                    <motion.div
                        className="absolute w-28 h-28 rounded-full border border-accent/20"
                        animate={{ rotate: -360 }}
                        transition={{ duration: 30, repeat: Infinity, ease: "linear" }}
                    />
                    <div className="relative w-20 h-20 rounded-full overflow-hidden bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center">
                        <img
                            src="/image/profile.png"
                            alt="CJ Profile"
                            className="w-full h-full object-cover"
                        />
                    </div>
                </div>
            </div>
        </BentoCard>
    );
};

// --- INTERACTIVE GLOBE COMPONENT ---
const DARK_GLOBE_CONFIG: COBEOptions = {
    width: 800,
    height: 800,
    onRender: () => {},
    devicePixelRatio: 2,
    phi: 0,
    theta: 0.3,
    dark: 1,
    diffuse: 1.2,
    mapSamples: 22000,
    mapBrightness: 2,
    baseColor: [0.8, 0.8, 0.8],
    markerColor: [1, 1, 1],
    glowColor: [0.1, 0.1, 0.15],
    markers: [
        { location: [8.62, 123.75], size: 0.1 }, 
        { location: [37.7749, -122.4194], size: 0.05 },
        { location: [51.5074, -0.1278], size: 0.05 },
        { location: [28.6139, 77.2090], size: 0.05 },
    ],
};

const InteractiveGlobe = ({ config = DARK_GLOBE_CONFIG }: { config?: COBEOptions }) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);

    useEffect(() => {
        let phi = 0;
        let globe: any;

        if (canvasRef.current) {
            globe = createGlobe(canvasRef.current, {
                ...config,
                onRender: (state) => {
                    if (!("ontouchstart" in window)) {
                         state.phi = phi;
                         phi += 0.003;
                    }
                    config.onRender(state);
                },
            });

            setTimeout(() => {
                if (canvasRef.current) canvasRef.current.style.opacity = '1';
            });
        }
        return () => globe?.destroy();
    }, [config]);

    return (
        <canvas
            ref={canvasRef}
            className="w-full h-full opacity-0 transition-opacity duration-500"
            style={{
                width: '100%',
                height: '100%',
                contain: 'layout paint size',
            }}
        />
    );
};

// --- MODIFIED: TIMEZONE CARD WITH FLOATING GLOBE ---
const TimezoneCard = () => (
    <motion.div
        variants={cardVariants}
        className={cn(
            // Remove overflow-hidden to allow globe to extend beyond boundaries
            "group relative flex flex-col bg-neutral-900/50 backdrop-blur-lg shadow-[inset_0_0_20px_0_rgba(255,255,255,0.08)] rounded-2xl p-6 transition-all duration-300 md:row-span-2 overflow-visible"
        )}
    >
        <div className="absolute inset-0 bg-gradient-to-br from-primary/4 via-transparent to-accent/4 rounded-2xl" />

        {/* Globe positioned inside the card but allowed to overflow */}
        <div className="absolute inset-x-0 bottom-0 flex h-3/4 items-center justify-center pointer-events-none z-0">
            <div className="w-[200%] max-w-[600px] aspect-square">
                <InteractiveGlobe />
            </div>
        </div>

        {/* Content container that sits on top of the globe */}
        <div className="relative z-10 flex h-full flex-col">
            {/* Top Content: Centered as requested */}
            <div className="text-center">
                <h3 className="text-xl font-semibold text-foreground mb-3">
                    I'm very flexible with time zone communications
                </h3>
                <div className="flex items-center justify-center gap-2 mb-4">
                    {['GB UK', 'IN India', 'us USA'].map((zone) => (
                        <div key={zone} className="px-3 py-1 text-xs font-medium text-muted-foreground bg-white/5 rounded-full border border-white/10">
                            {zone}
                        </div>
                    ))}
                </div>
            </div>

            {/* Spacer to push the footer to the bottom */}
            <div className="flex-1" />

            {/* Footer */}
            <div className="flex items-center justify-center gap-2 text-xs text-muted-foreground">
                <MapPin className="w-3 h-3" />
                <span className="font-geometric">Remote / Philippines</span>
            </div>
        </div>
    </motion.div>
);

// --- UPDATED CTA CARD ---
const CtaCard = () => (
    <BentoCard className="md:col-span-1 md:row-span-1">
        <div className="flex flex-col h-full justify-center items-center text-center">
            <div className="relative mb-6 flex justify-center">
                <img
                    src="/image/cj-transparent-white.png"
                    alt="CJ Logo"
                    className="w-8 h-8 object-contain"
                />
            </div>

            <h3 className="text-heading text-xl font-semibold text-foreground mb-2">Let's work together</h3>
            <p className="text-muted-foreground text-sm mb-6 font-geometric">on your next project.</p>

            <motion.a
                href="mailto:<EMAIL>"
                className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 rounded-xl text-primary-foreground text-sm font-semibold transition-all duration-300 shadow-lg hover:shadow-xl"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
            >
                <Mail className="w-4 h-4" />
                <span className="font-geometric"><EMAIL></span>
            </motion.a>
        </div>
    </BentoCard>
);

const TechStackCard = () => {
    const layers = useMemo(() => {
        const layerSize = Math.ceil(technicalSkills.length / 3);
        return [
            { techs: technicalSkills.slice(0, layerSize), direction: 1 },
            { techs: technicalSkills.slice(layerSize, layerSize * 2), direction: -1 },
            { techs: technicalSkills.slice(layerSize * 2), direction: 1 }
        ];
    }, []);

    const ICON_WIDTH = 64;
    const GAP_X = 16;
    const ITEM_TOTAL_WIDTH = ICON_WIDTH + GAP_X;
    const ANIMATION_DURATION = 20;

    return (
        <BentoCard className="md:row-span-2">
            <div className="flex flex-col h-full">
                <div className="flex items-center gap-3 mb-6">
                    <div className="p-2.5 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-xl backdrop-blur-sm border border-blue-500/20">
                        <Code2 className="w-5 h-5 text-accent" />
                    </div>
                    <div>
                        <h3 className="text-heading text-lg font-semibold text-foreground">Core Technologies</h3>
                        <p className="text-sm text-muted-foreground">Modern stack</p>
                    </div>
                </div>
                <div className="flex-1 overflow-hidden py-4">
                    <div className="space-y-3">
                        {layers.map((layer, layerIndex) => {
                            const extendedLayer = [...layer.techs, ...layer.techs, ...layer.techs];
                            const totalWidth = layer.techs.length * ITEM_TOTAL_WIDTH;
                            
                            return (
                                <div key={layerIndex} className="relative overflow-hidden">
                                    <motion.div
                                        className="flex items-center gap-x-4"
                                        animate={{ x: layer.direction === 1 ? [0, -totalWidth] : [-totalWidth, 0] }}
                                        transition={{
                                            x: {
                                                repeat: Infinity,
                                                repeatType: "loop",
                                                duration: ANIMATION_DURATION + (layerIndex * 2),
                                                ease: "linear",
                                            },
                                        }}
                                    >
                                        {extendedLayer.map((skill, index) => {
                                            const IconComponent = skill.icon;
                                            const isDarkIcon = skill.color === '#000000' || skill.color === 'black';
                                            const glowColor = isDarkIcon ? '#FFFFFF' : skill.color;

                                            return (
                                                <div key={`${skill.name}-${index}`} className="flex flex-shrink-0 flex-col items-center space-y-1 w-16">
                                                    <div className="w-10 h-10 flex items-center justify-center rounded-xl bg-gradient-to-br from-background/80 to-background/40 backdrop-blur-sm">
                                                        <IconComponent
                                                            className="w-6 h-6"
                                                            style={{
                                                                color: skill.color,
                                                                filter: `drop-shadow(0 0 6px ${glowColor}55)`
                                                            } as React.CSSProperties}
                                                        />
                                                    </div>
                                                    <p className="text-xs font-semibold text-muted-foreground text-center truncate w-full">
                                                        {skill.name}
                                                    </p>
                                                </div>
                                            );
                                        })}
                                    </motion.div>
                                </div>
                            );
                        })}
                    </div>
                </div>
                <div className="mt-auto space-y-3">
                    <div className="flex items-center justify-between text-xs">
                        <span className="text-muted-foreground">Experience</span>
                        <span className="text-foreground font-semibold">5+ Years</span>
                    </div>
                    <div className="flex items-center justify-between text-xs">
                        <span className="text-muted-foreground">Projects</span>
                        <span className="text-foreground font-semibold">50+ Completed</span>
                    </div>
                    <div className="w-full bg-secondary/50 rounded-full h-1.5">
                        <motion.div
                            className="bg-gradient-to-r from-primary to-accent h-1.5 rounded-full"
                            initial={{ width: 0 }}
                            whileInView={{ width: "95%" }}
                            transition={{ duration: 1.5, delay: 0.5 }}
                        />
                    </div>
                </div>
            </div>
        </BentoCard>
    );
};

const ServicesCard = () => {
    const CARD_WIDTH = 190;
    const GAP = 16;
    const TOTAL_WIDTH = (CARD_WIDTH + GAP) * services.length;
    const ANIMATION_DURATION = 45;

    return (
        <BentoCard className="md:col-span-2 md:row-span-1">
            <div className="flex flex-col h-full">
                <div className="mb-6">
                    <div className="flex items-center gap-2 mb-2">
                        <Sparkles className="w-4 h-4 text-accent" />
                        <span className="text-xs text-muted-foreground font-geometric uppercase tracking-wider">The Inside Scoop</span>
                    </div>
                    <h2 className="text-heading text-xl font-semibold text-foreground">Building a SaaS Application</h2>
                </div>
                <div className="flex-grow overflow-hidden relative">
                    <div className="absolute top-0 bottom-0 left-0 w-8 z-10 bg-gradient-to-r from-neutral-900/60 to-transparent" />
                    <div className="absolute top-0 bottom-0 right-0 w-8 z-10 bg-gradient-to-l from-neutral-900/60 to-transparent" />
                    
                    <motion.div
                        className="flex gap-4 pb-4"
                        animate={{ x: [0, -TOTAL_WIDTH] }}
                        transition={{
                            x: {
                                repeat: Infinity,
                                repeatType: "loop",
                                duration: ANIMATION_DURATION,
                                ease: "linear",
                            },
                        }}
                    >
                        {[...services, ...services].map((service, index) => {
                            const IconComponent = service.icon;
                            return (
                                <div
                                    key={`${service.title}-${index}`}
                                    className={cn(
                                        "relative p-4 rounded-xl border border-border/50 backdrop-blur-sm group flex-shrink-0",
                                        "bg-gradient-to-br", service.gradient
                                    )}
                                    style={{ width: `${CARD_WIDTH}px` }}
                                >
                                    <div className="flex items-center gap-2 mb-2">
                                        <div className="p-1.5 bg-background/50 rounded-lg">
                                            <IconComponent className="w-4 h-4 text-foreground" />
                                        </div>
                                        <h4 className="font-semibold text-foreground text-sm font-geometric">{service.title}</h4>
                                    </div>
                                    <p className="text-muted-foreground text-xs leading-relaxed font-geometric line-clamp-3">{service.description}</p>
                                </div>
                            );
                        })}
                    </motion.div>
                </div>
            </div>
        </BentoCard>
    );
};

// --- MAIN SECTION COMPONENT WITH REDUCED GRID HEIGHT ---
export default function CurrentFocusSection({ className }: BaseSectionProps) {
    return (
        <section className={cn('py-16 lg:py-24 bg-background text-foreground', className)} id="current-focus">
            <div className="container mx-auto px-4">
                <motion.div
                    className="text-center mb-12"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6 }}
                >
                    {/* Optional heading can be placed here if needed */}
                </motion.div>
                <motion.div
                    className="grid grid-cols-1 md:grid-cols-3 auto-rows-fr gap-6 max-w-7xl mx-auto"
                    variants={containerVariants}
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true, margin: "-100px" }}
                >
                    <CollaborationCard />
                    <TechStackCard />
                    <TimezoneCard />
                    <CtaCard />
                    <ServicesCard />
                </motion.div>
            </div>
        </section>
    );
}